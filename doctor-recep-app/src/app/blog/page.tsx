import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { getBlogPosts, urlFor } from '@/lib/sanity/client'
import { trackEvent } from '@/lib/analytics'
import { Calendar, Clock, ArrowRight, Sparkles, Wand2, BookOpen } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Blog - Celer AI',
  description: 'Latest insights, tips, and updates about AI-powered healthcare documentation',
  keywords: 'healthcare, AI, medical documentation, blog, insights',
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function BlogPage() {
  const posts = await getBlogPosts()

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Magical Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
            <Link
              href="/guide"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Guides
            </Link>
            <Link
              href="/login"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Sign In
            </Link>
          </div>
        </div>
      </nav>

      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Hero Section - Effortless Flow */}
      <main className="relative">
        <div className="relative max-w-7xl mx-auto px-6 pt-32 pb-16">
          <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[70vh]">

            {/* Left Column - Content flows naturally */}
            <div className="space-y-8">
              <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2">
                <Sparkles className="w-4 h-4 text-indigo-600 animate-pulse" />
                <span className="text-indigo-700 text-sm font-medium">AI Healthcare Insights</span>
                <Wand2 className="w-4 h-4 text-purple-600" />
              </div>

              <div className="space-y-4">
                <h1 className="text-6xl md:text-7xl font-black text-slate-900 leading-none">
                  Insights.
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse">
                    Flow.
                  </span>
                  <span className="block text-slate-700">
                    Effortless.
                  </span>
                </h1>

                <div className="flex items-center space-x-3 text-lg text-slate-600">
                  <BookOpen className="w-5 h-5 text-emerald-500" />
                  <span>Latest trends in AI healthcare documentation</span>
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-ping"></div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-slate-50 to-indigo-50 rounded-2xl p-6 border border-slate-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-full flex items-center justify-center">
                      <BookOpen className="w-6 h-6 text-white animate-pulse" />
                    </div>
                    <div>
                      <p className="font-semibold text-slate-800">Discover insights that</p>
                      <p className="text-slate-600 italic">&ldquo;Transform your healthcare practice effortlessly...&rdquo;</p>
                    </div>
                  </div>
                  <ArrowRight className="w-6 h-6 text-slate-400" />
                </div>
              </div>
            </div>

            {/* Right Column - Visual Flow */}
            <div className="relative">
              <div className="relative">
                <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-30 animate-pulse"></div>
                <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden p-8">
                  <div className="text-center space-y-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center mx-auto">
                      <BookOpen className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-slate-900 mb-2">Stay Informed</h3>
                      <p className="text-slate-600">Effortless insights that flow naturally into your practice</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4 pt-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-emerald-600">{posts.length}</div>
                        <div className="text-sm text-slate-600">Articles</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">∞</div>
                        <div className="text-sm text-slate-600">Insights</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Blog Posts Grid - Effortless Flow */}
      <section className="relative pb-20 px-6">
        <div className="max-w-7xl mx-auto">
          {posts.length === 0 ? (
            <div className="text-center py-16">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <Calendar className="w-12 h-12 text-indigo-600" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-400 rounded-full animate-ping"></div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-400 rounded-full"></div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-2">Insights Coming Soon</h3>
              <p className="text-slate-600 text-lg">Magical content flowing your way...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post, index) => (
                <div
                  key={post._id}
                  className="animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <BlogPostCard post={post} />
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  )
}

function BlogPostCard({ post }: { post: any }) {
  const publishedDate = new Date(post.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const handleClick = () => {
    // Track blog post view in public zone
    if (typeof window !== 'undefined') {
      import('@/lib/analytics').then(({ trackEvent }) => {
        trackEvent('blog_post_viewed', { page_title: post.title })
      })
    }
  }

  return (
    <article className="relative bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 overflow-hidden group border border-white/20">
      {/* Magical Glow Effect */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-sm"></div>

      {/* Featured Image */}
      {post.mainImage && (
        <div className="relative h-48 overflow-hidden">
          <Image
            src={urlFor(post.mainImage).width(400).height(300).fit('crop').auto('format').url()}
            alt={post.mainImage.alt || post.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-500"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

          {/* Floating Badge */}
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-lg">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-slate-700">New</span>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {/* Categories */}
        {post.categories && post.categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {post.categories.slice(0, 2).map((category: any) => (
              <span
                key={category.slug.current}
                className="px-3 py-1 bg-indigo-100 text-indigo-700 text-xs font-medium rounded-full"
              >
                {category.title}
              </span>
            ))}
          </div>
        )}

        {/* Title */}
        <h2 className="text-xl font-bold text-slate-900 mb-3 line-clamp-2 group-hover:text-indigo-600 transition-colors">
          {post.title}
        </h2>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-slate-600 mb-4 line-clamp-3 leading-relaxed">
            {post.excerpt}
          </p>
        )}

        {/* Meta */}
        <div className="flex items-center justify-between text-sm text-slate-500 mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>{publishedDate}</span>
            </div>
            {post.author && (
              <div className="flex items-center space-x-2">
                {post.author.image && (
                  <Image
                    src={urlFor(post.author.image).width(24).height(24).fit('crop').auto('format').url()}
                    alt={post.author.name}
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                )}
                <span>{post.author.name}</span>
              </div>
            )}
          </div>
        </div>

        {/* Read More Link */}
        <Link
          href={`/blog/${post.slug.current}`}
          onClick={handleClick}
          className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-800 font-medium transition-colors group"
        >
          <span>Read More</span>
          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
        </Link>
      </div>
    </article>
  )
}
